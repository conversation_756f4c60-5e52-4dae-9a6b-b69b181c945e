@extends('layouts.app')

@section('content')
    <x-card title="Editar Registro de Ação" :cardHeaderTitle="true" :backButton="true" :backRoute="route('action_records.index')">
        <x-form.edit-form :$resourceRoute :model="$actionRecord">
            <x-tabs id="action-record-edit">
                <x-slot name="tabHeaders">
                    <x-tabs.tab-header-item id="action-record-edit-general" text="Geral" :active="true" />
                    <x-tabs.tab-header-item id="action-record-edit-analysis" text="Análise" />
                    <x-tabs.tab-header-item id="action-record-edit-origins" text="Origens" />
                    <x-tabs.tab-header-item id="action-record-edit-actions" text="Ações" />
                </x-slot>
                <x-slot name="tabContent">
                    <x-tabs.tab-content-item id="action-record-edit-general" :active="true">
                        <x-form.section>
                            <x-form.row>
                                <x-input.select md="6" lg="6" :$resourceRoute :$action field="team_id" :required="true" :options="$teams" :value="$actionRecord->team_id" />
                                <x-input.text md="6" lg="6" :$resourceRoute :$action field="subject" :required="true" :value="$actionRecord->subject" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.textarea md="12" lg="12" :$resourceRoute :$action field="description" :required="true" rows="4" :value="$actionRecord->description" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.textarea md="12" lg="12" :$resourceRoute :$action field="immediate_action" :required="true" rows="3" :value="$actionRecord->immediate_action" />
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    
                    <x-tabs.tab-content-item id="action-record-edit-analysis">
                        <x-form.section>
                            <x-form.row>
                                <x-input.textarea md="6" lg="6" :$resourceRoute :$action field="consequences" :required="true" rows="4" :value="$actionRecord->consequences" />
                                <x-input.textarea md="6" lg="6" :$resourceRoute :$action field="causes" :required="true" rows="4" :value="$actionRecord->causes" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <div class="col-12">
                                    <h5>5 Porquês</h5>
                                </div>
                            </x-form.row>
                            <x-form.row>
                                <x-input.textarea md="12" lg="12" :$resourceRoute :$action field="five_whys_description" :required="true" rows="3" :value="$actionRecord->five_whys_description" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.textarea md="6" lg="6" :$resourceRoute :$action field="five_whys_infrastructure" :required="true" rows="3" :value="$actionRecord->five_whys_infrastructure" />
                                <x-input.textarea md="6" lg="6" :$resourceRoute :$action field="five_whys_personal" :required="true" rows="3" :value="$actionRecord->five_whys_personal" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.textarea md="6" lg="6" :$resourceRoute :$action field="five_whys_methods" :required="true" rows="3" :value="$actionRecord->five_whys_methods" />
                                <x-input.textarea md="6" lg="6" :$resourceRoute :$action field="five_whys_materials" :required="true" rows="3" :value="$actionRecord->five_whys_materials" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.checkbox md="4" lg="4" :$resourceRoute :$action field="valid_problem" :checked="$actionRecord->valid_problem" />
                                <x-input.checkbox md="4" lg="4" :$resourceRoute :$action field="similar_action_records_exist" :checked="$actionRecord->similar_action_records_exist" />
                                <x-input.checkbox md="4" lg="4" :$resourceRoute :$action field="send_to_client" :checked="$actionRecord->send_to_client" />
                            </x-form.row>
                            <x-form.row :marginTop="true">
                                <x-input.checkbox md="4" lg="4" :$resourceRoute :$action field="recurrent" :checked="$actionRecord->recurrent" />
                                <x-input.checkbox md="4" lg="4" :$resourceRoute :$action field="needs_to_update_risks_and_opportunities" :checked="$actionRecord->needs_to_update_risks_and_opportunities" />
                                <x-input.checkbox md="4" lg="4" :$resourceRoute :$action field="needs_to_change_qms" :checked="$actionRecord->needs_to_change_qms" />
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    
                    <x-tabs.tab-content-item id="action-record-edit-origins">
                        <x-form.section>
                            <div class="alert alert-info">
                                <i class="fa fa-info-circle mr-2"></i>
                                Selecione pelo menos uma origem para este registro de ação.
                            </div>
                            <x-form.row>
                                <div class="col-12">
                                    <label class="form-label required">Origens</label>
                                    <div class="row">
                                        @php
                                            $selectedOrigins = $actionRecord->actionRecordOrigins->pluck('name')->toArray();
                                        @endphp
                                        @foreach($origins as $value => $label)
                                            <div class="col-md-6 col-lg-4 mb-2">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="origins[]" value="{{ $value }}" id="origin_{{ $value }}" {{ in_array($value, $selectedOrigins) ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="origin_{{ $value }}">
                                                        {{ $label }}
                                                    </label>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                    
                    <x-tabs.tab-content-item id="action-record-edit-actions">
                        <x-form.section>
                            <div class="alert alert-info">
                                <i class="fa fa-info-circle mr-2"></i>
                                Adicione pelo menos uma ação a ser executada. O status será calculado automaticamente com base no prazo e data de conclusão.
                            </div>
                            <div id="action-items-container">
                                @foreach($actionRecord->actionRecordItems as $index => $item)
                                    <div class="action-item-row" data-index="{{ $index }}">
                                        <div class="row {{ $index > 0 ? 'mt-3' : '' }}">
                                            <div class="col-md-4">
                                                <label class="form-label required">Ação</label>
                                                <textarea class="form-control" name="action_items[{{ $index }}][action]" rows="2" required>{{ $item->action }}</textarea>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label required">Equipe Responsável</label>
                                                <select class="form-control" name="action_items[{{ $index }}][team_id]" required>
                                                    <option value="">Selecione uma equipe</option>
                                                    @foreach($teams as $team)
                                                        <option value="{{ $team->id }}" {{ $team->id == $item->team_id ? 'selected' : '' }}>{{ $team->name }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label required">Prazo</label>
                                                <input type="date" class="form-control" name="action_items[{{ $index }}][deadline]" value="{{ $item->deadline->format('Y-m-d') }}" required>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">Concluído em</label>
                                                <input type="date" class="form-control" name="action_items[{{ $index }}][finished_at]" value="{{ $item->finished_at ? $item->finished_at->format('Y-m-d') : '' }}">
                                            </div>
                                            <div class="col-md-2 d-flex align-items-end">
                                                <button type="button" class="btn btn-danger btn-sm remove-action-item" {{ count($actionRecord->actionRecordItems) <= 1 ? 'style=display:none;' : '' }}>
                                                    <i class="fa fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            <x-form.row :marginTop="true">
                                <div class="col-12">
                                    <button type="button" id="add-action-item" class="btn btn-success btn-sm">
                                        <i class="fa fa-plus mr-1"></i> Adicionar Ação
                                    </button>
                                </div>
                            </x-form.row>
                        </x-form.section>
                    </x-tabs.tab-content-item>
                </x-slot>
            </x-tabs>
        </x-form.edit-form>
    </x-card>
@endsection

@section('js-scripts')
    <script>
        let teams = @json($teams);
        
        $(document).ready(function() {
            let actionItemIndex = {{ count($actionRecord->actionRecordItems) }};

            // Add action item
            $('#add-action-item').click(function() {
                let teamsOptions = '<option value="">Selecione uma equipe</option>';
                teams.forEach(function(team) {
                    teamsOptions += `<option value="${team.id}">${team.name}</option>`;
                });
                
                const newRow = `
                    <div class="action-item-row" data-index="${actionItemIndex}">
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <label class="form-label required">Ação</label>
                                <textarea class="form-control" name="action_items[${actionItemIndex}][action]" rows="2" required></textarea>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label required">Equipe Responsável</label>
                                <select class="form-control" name="action_items[${actionItemIndex}][team_id]" required>
                                    ${teamsOptions}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label required">Prazo</label>
                                <input type="date" class="form-control" name="action_items[${actionItemIndex}][deadline]" required>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Concluído em</label>
                                <input type="date" class="form-control" name="action_items[${actionItemIndex}][finished_at]">
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="button" class="btn btn-danger btn-sm remove-action-item">
                                    <i class="fa fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                
                $('#action-items-container').append(newRow);
                actionItemIndex++;
                updateRemoveButtons();
            });

            // Remove action item
            $(document).on('click', '.remove-action-item', function() {
                $(this).closest('.action-item-row').remove();
                updateRemoveButtons();
            });

            function updateRemoveButtons() {
                const rows = $('.action-item-row');
                if (rows.length > 1) {
                    $('.remove-action-item').show();
                } else {
                    $('.remove-action-item').hide();
                }
            }
        });
    </script>
@endsection
