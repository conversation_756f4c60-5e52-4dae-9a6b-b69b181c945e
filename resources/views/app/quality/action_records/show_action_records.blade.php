@extends('layouts.app')

@section('content')
    <x-card title="Visualizar Registro de Ação" :cardHeaderTitle="true" :backButton="true" :backRoute="route('action_records.index')">
        <x-tabs id="action-record-show">
            <x-slot name="tabHeaders">
                <x-tabs.tab-header-item id="action-record-show-general" text="Geral" :active="true" />
                <x-tabs.tab-header-item id="action-record-show-analysis" text="Análise" />
                <x-tabs.tab-header-item id="action-record-show-origins" text="Origens" />
                <x-tabs.tab-header-item id="action-record-show-actions" text="Ações" />
            </x-slot>
            <x-slot name="tabContent">
                <x-tabs.tab-content-item id="action-record-show-general" :active="true">
                    <x-form.section>
                        <x-form.row>
                            <x-input.show md="6" lg="6" field="team_id" label="Equipe" :value="$actionRecord->team->name" />
                            <x-input.show md="6" lg="6" field="subject" label="Assunto" :value="$actionRecord->subject" />
                        </x-form.row>
                        <x-form.row :marginTop="true">
                            <x-input.show md="12" lg="12" field="description" label="Descrição" :value="$actionRecord->description" />
                        </x-form.row>
                        <x-form.row :marginTop="true">
                            <x-input.show md="12" lg="12" field="immediate_action" label="Ação Imediata" :value="$actionRecord->immediate_action" />
                        </x-form.row>
                        <x-form.row :marginTop="true">
                            <x-input.show md="6" lg="6" field="created_by" label="Criado por" :value="$actionRecord->created_by_operator_name" />
                            <x-input.show md="6" lg="6" field="created_at" label="Criado em" :value="format_datetime($actionRecord->created_at)" />
                        </x-form.row>
                        <x-form.row :marginTop="true">
                            <div class="col-md-6">
                                <label class="form-label">Status Geral</label>
                                <div>
                                    <span class="badge {{ $actionRecord->status_badge_class }}">{{ $actionRecord->status }}</span>
                                </div>
                            </div>
                        </x-form.row>
                    </x-form.section>
                </x-tabs.tab-content-item>
                
                <x-tabs.tab-content-item id="action-record-show-analysis">
                    <x-form.section>
                        <x-form.row>
                            <x-input.show md="6" lg="6" field="consequences" label="Consequências" :value="$actionRecord->consequences" />
                            <x-input.show md="6" lg="6" field="causes" label="Causas" :value="$actionRecord->causes" />
                        </x-form.row>
                        <x-form.row :marginTop="true">
                            <div class="col-12">
                                <h5>5 Porquês</h5>
                            </div>
                        </x-form.row>
                        <x-form.row>
                            <x-input.show md="12" lg="12" field="five_whys_description" label="Descrição" :value="$actionRecord->five_whys_description" />
                        </x-form.row>
                        <x-form.row :marginTop="true">
                            <x-input.show md="6" lg="6" field="five_whys_infrastructure" label="Infraestrutura" :value="$actionRecord->five_whys_infrastructure" />
                            <x-input.show md="6" lg="6" field="five_whys_personal" label="Pessoal" :value="$actionRecord->five_whys_personal" />
                        </x-form.row>
                        <x-form.row :marginTop="true">
                            <x-input.show md="6" lg="6" field="five_whys_methods" label="Métodos" :value="$actionRecord->five_whys_methods" />
                            <x-input.show md="6" lg="6" field="five_whys_materials" label="Materiais" :value="$actionRecord->five_whys_materials" />
                        </x-form.row>
                        <x-form.row :marginTop="true">
                            <div class="col-md-4">
                                <label class="form-label">Problema Válido</label>
                                <div>
                                    <span class="badge {{ $actionRecord->valid_problem ? 'badge-success' : 'badge-secondary' }}">
                                        {{ $actionRecord->valid_problem ? 'Sim' : 'Não' }}
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Registros Similares Existem</label>
                                <div>
                                    <span class="badge {{ $actionRecord->similar_action_records_exist ? 'badge-warning' : 'badge-secondary' }}">
                                        {{ $actionRecord->similar_action_records_exist ? 'Sim' : 'Não' }}
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Enviar ao Cliente</label>
                                <div>
                                    <span class="badge {{ $actionRecord->send_to_client ? 'badge-info' : 'badge-secondary' }}">
                                        {{ $actionRecord->send_to_client ? 'Sim' : 'Não' }}
                                    </span>
                                </div>
                            </div>
                        </x-form.row>
                        <x-form.row :marginTop="true">
                            <div class="col-md-4">
                                <label class="form-label">Recorrente</label>
                                <div>
                                    <span class="badge {{ $actionRecord->recurrent ? 'badge-warning' : 'badge-secondary' }}">
                                        {{ $actionRecord->recurrent ? 'Sim' : 'Não' }}
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Atualizar Riscos e Oportunidades</label>
                                <div>
                                    <span class="badge {{ $actionRecord->needs_to_update_risks_and_opportunities ? 'badge-info' : 'badge-secondary' }}">
                                        {{ $actionRecord->needs_to_update_risks_and_opportunities ? 'Sim' : 'Não' }}
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Alterar SGQ</label>
                                <div>
                                    <span class="badge {{ $actionRecord->needs_to_change_qms ? 'badge-info' : 'badge-secondary' }}">
                                        {{ $actionRecord->needs_to_change_qms ? 'Sim' : 'Não' }}
                                    </span>
                                </div>
                            </div>
                        </x-form.row>
                    </x-form.section>
                </x-tabs.tab-content-item>
                
                <x-tabs.tab-content-item id="action-record-show-origins">
                    <x-form.section>
                        <x-form.row>
                            <div class="col-12">
                                <label class="form-label">Origens</label>
                                <div class="mt-2">
                                    @foreach($actionRecord->actionRecordOrigins as $origin)
                                        <span class="badge badge-primary mr-2 mb-2">
                                            {{ \App\Enums\ActionRecordOriginEnum::getTranslated()[$origin->name] ?? $origin->name }}
                                        </span>
                                    @endforeach
                                </div>
                            </div>
                        </x-form.row>
                    </x-form.section>
                </x-tabs.tab-content-item>
                
                <x-tabs.tab-content-item id="action-record-show-actions">
                    <x-form.section>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Ação</th>
                                        <th>Equipe Responsável</th>
                                        <th>Prazo</th>
                                        <th>Concluído em</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($actionRecord->actionRecordItems as $item)
                                        <tr>
                                            <td>{{ $item->action }}</td>
                                            <td>{{ $item->team->name }}</td>
                                            <td>{{ format_date($item->deadline) }}</td>
                                            <td>{{ $item->finished_at ? format_date($item->finished_at) : '-' }}</td>
                                            <td>
                                                <span class="badge {{ $item->status_badge_class }}">{{ $item->status }}</span>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="5" class="text-center">Nenhuma ação cadastrada</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </x-form.section>
                </x-tabs.tab-content-item>
            </x-slot>
        </x-tabs>
    </x-card>
@endsection
