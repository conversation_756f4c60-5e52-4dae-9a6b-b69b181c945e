<?php

use App\Actions\ActionRecord\CreateActionRecord;
use App\Actions\ActionRecord\DeleteActionRecord;
use App\Actions\ActionRecord\DeleteActionRecords;
use App\Actions\ActionRecord\EditActionRecord;
use App\Actions\ActionRecord\GetActionRecord;
use App\Actions\ActionRecord\GetActionRecords;

Route::prefix('action-records')->group(function () {
    Route::get('', GetActionRecords::class)->name('action_records.index');
    Route::get('create', CreateActionRecord::class)->name('action_records.create');
    Route::post('', CreateActionRecord::class)->name('action_records.store');
    Route::delete('delete-batch', DeleteActionRecords::class)->name('action_records.delete_batch');
    Route::prefix('{action_record}')->group(function () {
        Route::get('', GetActionRecord::class)->name('action_records.show');
        Route::get('edit', EditActionRecord::class)->name('action_records.edit');
        Route::put('', EditActionRecord::class)->name('action_records.update');
        Route::delete('', DeleteActionRecord::class)->name('action_records.destroy');
    });
});
