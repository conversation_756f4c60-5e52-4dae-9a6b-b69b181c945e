<?php

declare(strict_types=1);

use App\Actions\CalculateInstallments;
use App\Actions\Receivable\GetBankSlipFromErpFlex;
use App\Core\Module;
use App\Http\Controllers\HomeController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| Here you can register the tenant routes for your application.
| These routes are loaded by the TenantRouteServiceProvider.
|
| Feel free to customize them however you want. Good luck!
|
*/

Route::get('privacy-policy', fn () => view('general.privacy_policy'));

Route::middleware([
    'web',
    InitializeTenancyByDomain::class,
    PreventAccessFromCentralDomains::class,
])->group(function () {
    Route::middleware('universal')->group(function () {
        Auth::routes(['register' => false]);

        Route::middleware(['auth', 'verify_csrf_token'])->group(function () {
            Route::middleware([
                'verify_existing_management_parameters',
                'verify_existing_billing_parameters',
                'verify_existing_contract_parameters',
                'verify_existing_crm_parameters',
                'verify_existing_finance_parameters',
                'verify_existing_engineering_parameters',
                'verify_active_user'
            ])->group(function () {
                Route::get('home', [HomeController::class, 'loadHomeView'])->name('home');
                Route::get('get-news', [HomeController::class, 'getNews'])->name('get_news');
                Route::post('calculate-installments', CalculateInstallments::class)->name('calculate_installments');

                Route::prefix(Module::MANAGEMENT)->group(__DIR__ . '/tenant/management.php');
                Route::prefix(Module::CONTRACT)->group(__DIR__ . '/tenant/contracts.php');
                Route::prefix(Module::ACCREDITATION)->group(__DIR__ . '/tenant/accreditation.php');
                Route::prefix(Module::PROVIDING)->group(__DIR__ . '/tenant/providing.php');
                Route::prefix(Module::CRM)->group(__DIR__ . '/tenant/crm.php');
                Route::prefix(Module::AFTER_SALES)->group(__DIR__ . '/tenant/after_sales.php');
                Route::prefix(Module::BILLING)->group(__DIR__ . '/tenant/billing.php');
                Route::prefix(Module::FINANCE)->group(__DIR__ . '/tenant/finance.php');
                Route::prefix(Module::OPERATION)->group(__DIR__ . '/tenant/operation.php');
                Route::prefix(Module::ENGINEERING)->group(__DIR__ . '/tenant/engineering.php');
                Route::prefix(Module::KNOWLEDGE_BASE)->group(__DIR__ . '/tenant/knowledge_base.php');
                Route::prefix(Module::QUALITY)->group(__DIR__ . '/tenant/quality.php');
                Route::prefix(Module::TICKETS)->group(__DIR__ . '/tenant/tickets.php');
                Route::prefix(Module::REPORTS)->group(__DIR__ . '/tenant/reports.php');
                Route::prefix(Module::IMPORTS)->group(__DIR__ . '/tenant/imports.php');
                Route::prefix(Module::LOGS)->group(__DIR__ . '/tenant/logs.php');
                Route::prefix(Module::SETTINGS)->group(__DIR__ . '/tenant/settings.php');
                Route::prefix(Module::PARAMETERS)->group(__DIR__ . '/tenant/parameters.php');
                Route::prefix(Module::AUDITING)->group(__DIR__ . '/tenant/auditing.php');
                Route::prefix(Module::INTEGRATIONS)->group(__DIR__ . '/tenant/integrations.php');
            });
        });

        Route::get('finance/receivables/get-bank-slip-from-erp-flex/{receivableToken}', GetBankSlipFromErpFlex::class)
            ->name('receivables.get_bank_slip_from_erp_flex');

        Route::prefix('external')->group(__DIR__ . '/tenant/external.php');

        Route::prefix('webhook')->group(__DIR__ . '/tenant/webhook.php');
    });

    Route::get('/', function () {
        return redirect()->route('login');
    });
});
