<?php

namespace App\Actions\ActionRecord;

use App\Models\ActionRecord;
use App\Models\Permission;
use App\Models\Team;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GetActionRecord
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::GET_ACTION_RECORDS);
    }

    public function handle(ActionRecord $actionRecord): Factory|View
    {
        try {
            return $actionRecord->getBackEndActionGeneratorInstance()->loadShowView([
                'actionRecord' => $actionRecord->load([
                    'team',
                    'creationOperator',
                    'actionRecordItems.team',
                    'actionRecordOrigins'
                ]),
                'teams' => Team::query()
                    ->select(['id', 'name'])
                    ->orderBy('name')
                    ->get()
                    ->mapWithKeys(fn(Team $team) => [$team->id => $team->name])
                    ->toArray(),
            ]);
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
