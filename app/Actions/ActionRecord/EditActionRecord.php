<?php

namespace App\Actions\ActionRecord;

use App\Enums\ActionRecordOriginEnum;
use App\Models\ActionRecord;
use App\Models\ActionRecordItem;
use App\Models\ActionRecordLog;
use App\Models\ActionRecordOrigin;
use App\Models\Permission;
use App\Models\Team;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class EditActionRecord
{
    use AsAction;

    protected array $rules = [
        'team_id' => 'required|exists:teams,id',
        'subject' => 'required|string|max:500',
        'description' => 'required|string',
        'immediate_action' => 'required|string',
        'consequences' => 'required|string',
        'causes' => 'required|string',
        'five_whys_description' => 'required|string',
        'five_whys_infrastructure' => 'required|string',
        'five_whys_personal' => 'required|string',
        'five_whys_methods' => 'required|string',
        'five_whys_materials' => 'required|string',
        'valid_problem' => 'required|boolean',
        'similar_action_records_exist' => 'required|boolean',
        'send_to_client' => 'required|boolean',
        'recurrent' => 'required|boolean',
        'needs_to_update_risks_and_opportunities' => 'required|boolean',
        'needs_to_change_qms' => 'required|boolean',
        'origins' => 'required|array|min:1',
        'origins.*' => 'required|string|in:' . implode(',', array_column(ActionRecordOriginEnum::cases(), 'value')),
        'action_items' => 'required|array|min:1',
        'action_items.*.action' => 'required|string',
        'action_items.*.team_id' => 'required|exists:teams,id',
        'action_items.*.deadline' => 'required|date',
        'action_items.*.finished_at' => 'nullable|date',
    ];

    protected array $messages = [
        'team_id.required' => 'É obrigatório informar a equipe.',
        'team_id.exists' => 'A equipe informada não existe.',
        'subject.required' => 'É obrigatório informar o assunto.',
        'subject.max' => 'O assunto deve ter no máximo 500 caracteres.',
        'description.required' => 'É obrigatório informar a descrição.',
        'immediate_action.required' => 'É obrigatório informar a ação imediata.',
        'consequences.required' => 'É obrigatório informar as consequências.',
        'causes.required' => 'É obrigatório informar as causas.',
        'five_whys_description.required' => 'É obrigatório informar a descrição dos 5 porquês.',
        'five_whys_infrastructure.required' => 'É obrigatório informar a infraestrutura dos 5 porquês.',
        'five_whys_personal.required' => 'É obrigatório informar o pessoal dos 5 porquês.',
        'five_whys_methods.required' => 'É obrigatório informar os métodos dos 5 porquês.',
        'five_whys_materials.required' => 'É obrigatório informar os materiais dos 5 porquês.',
        'valid_problem.required' => 'É obrigatório informar se é um problema válido.',
        'similar_action_records_exist.required' => 'É obrigatório informar se existem registros de ação similares.',
        'send_to_client.required' => 'É obrigatório informar se deve enviar ao cliente.',
        'recurrent.required' => 'É obrigatório informar se é recorrente.',
        'needs_to_update_risks_and_opportunities.required' => 'É obrigatório informar se precisa atualizar riscos e oportunidades.',
        'needs_to_change_qms.required' => 'É obrigatório informar se precisa alterar o SGQ.',
        'origins.required' => 'É obrigatório informar pelo menos uma origem.',
        'origins.min' => 'É obrigatório informar pelo menos uma origem.',
        'origins.*.required' => 'É obrigatório informar a origem.',
        'origins.*.in' => 'A origem informada não é válida.',
        'action_items.required' => 'É obrigatório informar pelo menos um item de ação.',
        'action_items.min' => 'É obrigatório informar pelo menos um item de ação.',
        'action_items.*.action.required' => 'É obrigatório informar a ação.',
        'action_items.*.team_id.required' => 'É obrigatório informar a equipe responsável.',
        'action_items.*.team_id.exists' => 'A equipe responsável informada não existe.',
        'action_items.*.deadline.required' => 'É obrigatório informar o prazo.',
        'action_items.*.deadline.date' => 'O prazo deve ser uma data válida.',
        'action_items.*.finished_at.date' => 'A data de conclusão deve ser uma data válida.',
    ];

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::UPDATE_ACTION_RECORDS);
    }

    public function asController(ActionRequest $request, ActionRecord $actionRecord): mixed
    {
        if ($request->method() === Request::METHOD_GET) {
            return $actionRecord->getBackendActionGeneratorInstance()->loadEditView([
                'actionRecord' => $actionRecord->load(['actionRecordItems', 'actionRecordOrigins']),
                'teams' => Team::getForDropdown(),
                'origins' => ActionRecordOriginEnum::getTranslated(),
            ]);
        }

        $validator = Validator::make($request->all(), $this->rules, $this->messages);
        $validator->validate();

        try {
            $this->handle($actionRecord, $validator->validated());
            return redirect_success('action_records.index', __('action_records.responses.update.success'));
        } catch (Throwable $th) {
            return redirect_error($th->getMessage());
        }
    }

    public function handle(ActionRecord $actionRecord, array $data): ActionRecord
    {
        try {
            return DB::transaction(function () use ($actionRecord, $data) {
                // Store original data for logging
                $originalData = $actionRecord->toArray();

                // Update the action record
                $actionRecord->update([
                    'team_id' => $data['team_id'],
                    'subject' => $data['subject'],
                    'description' => $data['description'],
                    'immediate_action' => $data['immediate_action'],
                    'consequences' => $data['consequences'],
                    'causes' => $data['causes'],
                    'five_whys_description' => $data['five_whys_description'],
                    'five_whys_infrastructure' => $data['five_whys_infrastructure'],
                    'five_whys_personal' => $data['five_whys_personal'],
                    'five_whys_methods' => $data['five_whys_methods'],
                    'five_whys_materials' => $data['five_whys_materials'],
                    'valid_problem' => $data['valid_problem'],
                    'similar_action_records_exist' => $data['similar_action_records_exist'],
                    'send_to_client' => $data['send_to_client'],
                    'recurrent' => $data['recurrent'],
                    'needs_to_update_risks_and_opportunities' => $data['needs_to_update_risks_and_opportunities'],
                    'needs_to_change_qms' => $data['needs_to_change_qms'],
                ]);

                // Update action record origins
                $actionRecord->actionRecordOrigins()->delete();
                foreach ($data['origins'] as $origin) {
                    ActionRecordOrigin::create([
                        'action_record_id' => $actionRecord->id,
                        'name' => $origin,
                    ]);
                }

                // Update action record items
                $actionRecord->actionRecordItems()->delete();
                foreach ($data['action_items'] as $item) {
                    ActionRecordItem::create([
                        'action_record_id' => $actionRecord->id,
                        'action' => $item['action'],
                        'team_id' => $item['team_id'],
                        'deadline' => $item['deadline'],
                        'finished_at' => $item['finished_at'] ?? null,
                    ]);
                }

                // Log the update
                ActionRecordLog::create([
                    'action_record_id' => $actionRecord->id,
                    'operator_id' => auth()->id(),
                    'operator_name' => auth()->user()->operator->name,
                    'action' => ActionRecordLog::ACTION_UPDATED,
                    'data' => [
                        'subject' => $actionRecord->subject,
                        'team_id' => $actionRecord->team_id,
                        'origins_count' => count($data['origins']),
                        'action_items_count' => count($data['action_items']),
                        'changes' => array_diff_assoc($actionRecord->toArray(), $originalData),
                    ],
                ]);

                return $actionRecord;
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
