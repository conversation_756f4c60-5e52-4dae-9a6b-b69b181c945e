<?php

namespace App\Actions\ActionRecord;

use App\Models\ActionRecord;
use App\Models\Permission;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;

class GetActionRecords
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::GET_ACTION_RECORDS);
    }

    public function handle(): Factory|View
    {
        return ActionRecord::getBackEndActionGenerator()->loadIndexView();
    }
}
