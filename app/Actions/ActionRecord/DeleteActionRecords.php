<?php

namespace App\Actions\ActionRecord;

use App\Core\Http\Requests\DeleteBatchRequest;
use App\Models\ActionRecord;
use App\Models\Permission;
use Illuminate\Http\RedirectResponse;
use Lorisleiva\Actions\ActionRequest;
use Lori<PERSON><PERSON>\Actions\Concerns\AsAction;
use Throwable;

class DeleteActionRecords
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::DELETE_ACTION_RECORDS);
    }

    public function asController(DeleteBatchRequest $request): RedirectResponse
    {
        try {
            $this->handle($request->getDeleteIdsArray());
            return delete_batch_redirect_success('action_records.index');
        } catch (Throwable $th) {
            return redirect_error($th->getMessage());
        }
    }

    public function handle(array $ids)
    {
        try {
            collect($ids)->each(function (string $id): void {
                DeleteActionRecord::run(ActionRecord::find($id));
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
