<?php

namespace App\Actions\ActionRecord;

use App\Models\ActionRecord;
use App\Models\ActionRecordLog;
use App\Models\Permission;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;
use Lorisleiva\Actions\ActionRequest;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class DeleteActionRecord
{
    use AsAction;

    public function authorize(ActionRequest $request): bool
    {
        return $request->user()->can(Permission::DELETE_ACTION_RECORDS);
    }

    public function asController(ActionRecord $actionRecord): RedirectResponse
    {
        try {
            $this->handle($actionRecord);
            return redirect_success('action_records.index', __('action_records.responses.delete.success'));
        } catch (Throwable $th) {
            return redirect_error($th->getMessage());
        }
    }

    public function handle(ActionRecord $actionRecord)
    {
        try {
            DB::transaction(function () use ($actionRecord) {
                // Log the deletion
                ActionRecordLog::create([
                    'action_record_id' => $actionRecord->id,
                    'operator_id' => auth()->id(),
                    'operator_name' => auth()->user()->operator->name,
                    'action' => ActionRecordLog::ACTION_DELETED,
                    'data' => [
                        'subject' => $actionRecord->subject,
                        'team_id' => $actionRecord->team_id,
                        'created_by_operator_id' => $actionRecord->created_by_operator_id,
                        'deleted_at' => now(),
                    ],
                ]);

                // Delete related records
                $actionRecord->actionRecordItems()->delete();
                $actionRecord->actionRecordOrigins()->delete();
                
                // Delete the action record
                $actionRecord->delete();
            });
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
