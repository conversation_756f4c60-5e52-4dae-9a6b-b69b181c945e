<?php

namespace App\Models\Concerns\ActionRecordLog;

use App\Models\ActionRecord;
use App\Models\Operator;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesActionRecordLogRelationships
{
    public function actionRecord(): BelongsTo
    {
        return $this->belongsTo(ActionRecord::class);
    }

    public function operator(): BelongsTo
    {
        return $this->belongsTo(Operator::class);
    }
}
