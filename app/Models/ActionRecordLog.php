<?php

namespace App\Models;

use App\Core\Module;
use App\Models\Concerns\ActionRecordLog\HandlesActionRecordLogRelationships;

/**
 * Action record log model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $action_record_id
 * @property  int $operator_id
 * @property  string $operator_name
 * @property  string $action
 * @property  array $data
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\ActionRecord $actionRecord
 * @property  \App\Models\Operator $operator
 */
class ActionRecordLog extends Model
{
    use HandlesActionRecordLogRelationships;

    public const MODULE = Module::QUALITY;
    public const RESOURCE_ROUTE = 'action_record_logs';

    public const ACTION_CREATED = 'created';
    public const ACTION_UPDATED = 'updated';
    public const ACTION_DELETED = 'deleted';

    protected $fillable = [
        'action_record_id',
        'operator_id',
        'operator_name',
        'action',
        'data',
    ];

    protected $casts = [
        'data' => 'array',
    ];
}
