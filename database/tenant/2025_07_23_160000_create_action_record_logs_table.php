<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('action_record_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('action_record_id')->constrained();
            $table->foreignId('operator_id')->constrained();
            $table->string('operator_name');
            $table->string('action'); // created, updated, deleted
            $table->json('data')->nullable(); // Store relevant data for the action
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('action_record_logs');
    }
};
